## Create a plan to create a front end for an app

I want to implement a generic front end for an app. ( to be developed later)

Let's create a plan to start create the project front end. 

Step 1. Establish context:
	examine docs\Tailwind CSS TypeScript - Flowbite.md  and docs\design\Tech Stack.md

Step 2. ask any relevant questions

Step 3. Create an implementation plan in a docs/impl folder call front_end_implementation.md

Step 4. I will review the plan and ask questions

Step 5. Test the front end.


## Create a layout to test the daisy UI components
after reading docs\status.md and /impl/front_end_implementation.md
 to establish context.

I would like to create a layout to test the daisy UI components.

1. Sidebar navigation
2. Header
3. Footer
4. Main content
5. Modal
6. Toast
7. Alert
8. Card
9. But<PERSON>
10. Input
11. Select
12. Checkbox
13. Radio
14. Switch
15. Slider
16. Progress
17. Accordion
18. Tabs
19. Tooltip
20. Popover
21. Dropdown
