{"main": {"id": "314c8227291b3052", "type": "split", "children": [{"id": "8ad25e7baeb1aae1", "type": "tabs", "children": [{"id": "56558beafe2f73be", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Install a New React + TypeScript Project.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Install a New React + TypeScript Project"}}]}], "direction": "vertical"}, "left": {"id": "2d89bee07bf8ba5f", "type": "split", "children": [{"id": "9a8c3b6fceb1a51e", "type": "tabs", "children": [{"id": "56a228295f211a1b", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "ce8afdcd358cbd6a", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "5c8eade80bad6347", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "0ecfc7e34fe39a74", "type": "split", "children": [{"id": "a60de21b0025c968", "type": "tabs", "children": [{"id": "c7aacfa596b03f16", "type": "leaf", "state": {"type": "backlink", "state": {"file": "design/Tech Stack.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Tech Stack"}}, {"id": "ce4a629efd7f5920", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "design/Tech Stack.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Tech Stack"}}, {"id": "79e9bc9bca711e48", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "fab4f28ab88ee589", "type": "leaf", "state": {"type": "outline", "state": {"file": "Install a New React + TypeScript Project.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Install a New React + TypeScript Project"}}], "currentTab": 3}], "direction": "horizontal", "width": 300}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "bases:Create new base": false}}, "active": "56558beafe2f73be", "lastOpenFiles": ["impl/back_end_implementation.md", "design/Prompts.md", "status.md", "design/front_end_design_notes.md", "impl/front_end_implementation.md", "impl", "Tailwind CSS TypeScript - Flowbite.md", "design/Tech Stack.md", "Install a New React + TypeScript Project.md", "Install daisyUI for React.md", "design/Bob A llama.md", "design"]}