import { useState } from 'react'

interface LogEntry {
  id: string
  timestamp: string
  level: 'error' | 'warning' | 'info' | 'debug'
  message: string
  source: string
  details?: string
}

const mockLogs: LogEntry[] = [
  {
    id: '1',
    timestamp: '2025-01-11 10:30:15',
    level: 'error',
    message: 'Failed to connect to database',
    source: 'DatabaseService',
    details: 'Connection timeout after 30 seconds. Check database server status.'
  },
  {
    id: '2',
    timestamp: '2025-01-11 10:29:45',
    level: 'warning',
    message: 'High memory usage detected',
    source: 'SystemMonitor',
    details: 'Memory usage at 85%. Consider scaling resources.'
  },
  {
    id: '3',
    timestamp: '2025-01-11 10:29:12',
    level: 'info',
    message: 'User authentication successful',
    source: 'AuthService',
    details: 'User ID: 12345, Session: abc-def-ghi'
  },
  {
    id: '4',
    timestamp: '2025-01-11 10:28:33',
    level: 'debug',
    message: 'Processing task queue',
    source: 'TaskWorker',
    details: 'Queue size: 15, Processing time: 2.3s'
  },
  {
    id: '5',
    timestamp: '2025-01-11 10:27:58',
    level: 'info',
    message: 'API request completed',
    source: 'APIGateway',
    details: 'GET /api/v1/users - 200 OK - 145ms'
  }
]

export default function Logs() {
  const [selectedLevel, setSelectedLevel] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null)
  const [isRealTime, setIsRealTime] = useState(false)

  const filteredLogs = mockLogs.filter(log => {
    const matchesLevel = selectedLevel === 'all' || log.level === selectedLevel
    const matchesSearch = log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.source.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesLevel && matchesSearch
  })

  const getLevelBadgeClass = (level: string) => {
    switch (level) {
      case 'error': return 'badge-error'
      case 'warning': return 'badge-warning'
      case 'info': return 'badge-info'
      case 'debug': return 'badge-neutral'
      default: return 'badge-ghost'
    }
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">System Logs</h1>
        <p className="text-base-content/70">Monitor and analyze system events and messages</p>
      </div>

      {/* Controls */}
      <div className="card bg-base-100 shadow-xl mb-6">
        <div className="card-body">
          <div className="flex flex-wrap gap-4 items-center">
            {/* Level Filter */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Log Level</span>
              </label>
              <select 
                className="select select-bordered w-full max-w-xs"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                <option value="all">All Levels</option>
                <option value="error">Error</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
              </select>
            </div>

            {/* Search */}
            <div className="form-control flex-1">
              <label className="label">
                <span className="label-text">Search</span>
              </label>
              <input 
                type="text" 
                placeholder="Search logs..." 
                className="input input-bordered w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Real-time Toggle */}
            <div className="form-control">
              <label className="label cursor-pointer">
                <span className="label-text mr-2">Real-time</span>
                <input 
                  type="checkbox" 
                  className="toggle toggle-primary" 
                  checked={isRealTime}
                  onChange={(e) => setIsRealTime(e.target.checked)}
                />
              </label>
            </div>

            {/* Export Button */}
            <div className="form-control">
              <label className="label">
                <span className="label-text opacity-0">Export</span>
              </label>
              <button className="btn btn-outline">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Logs Table */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="overflow-x-auto">
            <table className="table table-zebra">
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>Level</th>
                  <th>Source</th>
                  <th>Message</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredLogs.map((log) => (
                  <tr key={log.id} className="hover">
                    <td className="font-mono text-sm">{log.timestamp}</td>
                    <td>
                      <div className={`badge ${getLevelBadgeClass(log.level)}`}>
                        {log.level.toUpperCase()}
                      </div>
                    </td>
                    <td className="font-medium">{log.source}</td>
                    <td className="max-w-md truncate">{log.message}</td>
                    <td>
                      <button 
                        className="btn btn-ghost btn-sm"
                        onClick={() => setSelectedLog(log)}
                      >
                        Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredLogs.length === 0 && (
            <div className="text-center py-8">
              <div className="text-base-content/50">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p>No logs found matching your criteria</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Log Details Modal */}
      {selectedLog && (
        <dialog className="modal modal-open">
          <div className="modal-box max-w-2xl">
            <h3 className="font-bold text-lg mb-4">Log Details</h3>
            
            <div className="space-y-4">
              <div>
                <label className="label">
                  <span className="label-text font-semibold">Timestamp</span>
                </label>
                <div className="font-mono text-sm">{selectedLog.timestamp}</div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text font-semibold">Level</span>
                </label>
                <div className={`badge ${getLevelBadgeClass(selectedLog.level)}`}>
                  {selectedLog.level.toUpperCase()}
                </div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text font-semibold">Source</span>
                </label>
                <div>{selectedLog.source}</div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text font-semibold">Message</span>
                </label>
                <div className="bg-base-200 p-3 rounded">{selectedLog.message}</div>
              </div>

              {selectedLog.details && (
                <div>
                  <label className="label">
                    <span className="label-text font-semibold">Details</span>
                  </label>
                  <div className="bg-base-200 p-3 rounded font-mono text-sm">{selectedLog.details}</div>
                </div>
              )}
            </div>

            <div className="modal-action">
              <button className="btn" onClick={() => setSelectedLog(null)}>Close</button>
            </div>
          </div>
        </dialog>
      )}
    </div>
  )
}
