import { Link } from 'react-router-dom'

export default function Dashboard() {
  const navigationCards = [
    {
      title: 'System Logs',
      description: 'Monitor and analyze system events and messages',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      link: '/logs',
      color: 'bg-primary'
    },
    {
      title: 'Component Test',
      description: 'Test and preview DaisyUI components',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      ),
      link: '/component-test',
      color: 'bg-secondary'
    },
    {
      title: 'Configuration',
      description: 'Manage system settings and preferences',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      link: '/configuration',
      color: 'bg-accent'
    },
    {
      title: 'System Info',
      description: 'View system status and health metrics',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      link: '/system',
      color: 'bg-info'
    },
    {
      title: 'Statistics',
      description: 'View analytics and performance metrics',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      link: '/stats',
      color: 'bg-success'
    }
  ]

  const systemStats = [
    { label: 'System Status', value: 'Online', color: 'text-success' },
    { label: 'Active Users', value: '1,234', color: 'text-info' },
    { label: 'CPU Usage', value: '45%', color: 'text-warning' },
    { label: 'Memory Usage', value: '67%', color: 'text-error' }
  ]

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Welcome Section */}
      <div className="hero bg-gradient-to-r from-primary to-secondary rounded-box mb-8">
        <div className="hero-content text-center text-primary-content">
          <div className="max-w-md">
            <h1 className="mb-5 text-5xl font-bold">Welcome</h1>
            <p className="mb-5">
              Monitor and manage your system with this comprehensive dashboard. 
              Navigate through different sections to access logs, configuration, and system information.
            </p>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {systemStats.map((stat, index) => (
          <div key={index} className="stat bg-base-100 shadow-lg rounded-box">
            <div className="stat-title">{stat.label}</div>
            <div className={`stat-value text-2xl ${stat.color}`}>{stat.value}</div>
          </div>
        ))}
      </div>

      {/* Navigation Cards */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-6">Quick Navigation</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {navigationCards.map((card, index) => (
            <Link key={index} to={card.link} className="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
              <div className="card-body">
                <div className={`w-16 h-16 ${card.color} rounded-full flex items-center justify-center text-white mb-4`}>
                  {card.icon}
                </div>
                <h3 className="card-title">{card.title}</h3>
                <p className="text-base-content/70">{card.description}</p>
                <div className="card-actions justify-end">
                  <button className="btn btn-primary btn-sm">
                    Open
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h3 className="card-title mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="badge badge-success">INFO</div>
              <span className="flex-1">System startup completed successfully</span>
              <span className="text-sm text-base-content/50">2 minutes ago</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="badge badge-warning">WARN</div>
              <span className="flex-1">High memory usage detected</span>
              <span className="text-sm text-base-content/50">5 minutes ago</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="badge badge-info">INFO</div>
              <span className="flex-1">User authentication successful</span>
              <span className="text-sm text-base-content/50">8 minutes ago</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="badge badge-error">ERROR</div>
              <span className="flex-1">Database connection timeout</span>
              <span className="text-sm text-base-content/50">12 minutes ago</span>
            </div>
          </div>
          <div className="card-actions justify-end mt-4">
            <Link to="/logs" className="btn btn-outline btn-sm">View All Logs</Link>
          </div>
        </div>
      </div>
    </div>
  )
}
