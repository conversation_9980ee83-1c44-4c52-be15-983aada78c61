import { useState, useRef, useEffect } from 'react'

export default function ComponentTest() {
  const [count, setCount] = useState(0)
  const [modalOpen, setModalOpen] = useState(false)
  const [toastVisible, setToastVisible] = useState(false)
  const [selectedTab, setSelectedTab] = useState('tab1')
  const [accordionOpen, setAccordionOpen] = useState('item1')
  const [sliderValue, setSliderValue] = useState(50)
  const [progressValue, setProgressValue] = useState(75)
  const [inputValue, setInputValue] = useState('')
  const [selectValue, setSelectValue] = useState('option1')
  const [checkboxChecked, setCheckboxChecked] = useState(false)
  const [radioValue, setRadioValue] = useState('radio1')
  const [switchChecked, setSwitchChecked] = useState(false)
  const [popoverOpen, setPopoverOpen] = useState(false)
  const popoverRef = useRef<HTMLDivElement>(null)

  const showToast = () => {
    setToastVisible(true)
    setTimeout(() => setToastVisible(false), 3000)
  }

  const toggleAccordion = (item: string) => {
    setAccordionOpen(accordionOpen === item ? '' : item)
  }

  const togglePopover = () => {
    setPopoverOpen(!popoverOpen)
  }

  // Close popover when clicking outside
  const handleClickOutside = (event: MouseEvent) => {
    if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
      setPopoverOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Alert */}
      <div className="alert alert-info mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Welcome to the DaisyUI Component Testing Layout!</span>
      </div>

      {/* Card */}
      <div className="card bg-base-100 shadow-xl mb-6">
        <div className="card-body">
          <h2 className="card-title">Basic Components</h2>
          
          {/* Button */}
          <div className="flex flex-wrap gap-2 mb-4">
            <button className="btn btn-primary" onClick={() => setCount(count + 1)}>
              Count: {count}
            </button>
            <button className="btn btn-secondary" onClick={() => setModalOpen(true)}>
              Open Modal
            </button>
            <button className="btn btn-accent" onClick={showToast}>
              Show Toast
            </button>
            <button className="btn btn-outline">Outline</button>
            <button className="btn btn-ghost">Ghost</button>
            <button className="btn btn-link">Link</button>
          </div>

          {/* Input */}
          <div className="flex flex-wrap gap-4 mb-4">
            <input 
              type="text" 
              placeholder="Type here" 
              className="input input-bordered w-full max-w-xs" 
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
            
            {/* Select */}
            <select 
              className="select select-bordered w-full max-w-xs"
              value={selectValue}
              onChange={(e) => setSelectValue(e.target.value)}
            >
              <option value="option1">Option 1</option>
              <option value="option2">Option 2</option>
              <option value="option3">Option 3</option>
            </select>
          </div>

          {/* Checkbox, Radio, Switch */}
          <div className="flex flex-wrap gap-6 mb-4">
            <label className="label cursor-pointer gap-2">
              <input 
                type="checkbox" 
                className="checkbox" 
                checked={checkboxChecked}
                onChange={(e) => setCheckboxChecked(e.target.checked)}
              />
              <span className="label-text">Checkbox</span>
            </label>

            <label className="label cursor-pointer gap-2">
              <input 
                type="radio" 
                name="radio-options" 
                className="radio" 
                checked={radioValue === 'radio1'}
                onChange={() => setRadioValue('radio1')}
              />
              <span className="label-text">Radio 1</span>
            </label>

            <label className="label cursor-pointer gap-2">
              <input 
                type="radio" 
                name="radio-options" 
                className="radio" 
                checked={radioValue === 'radio2'}
                onChange={() => setRadioValue('radio2')}
              />
              <span className="label-text">Radio 2</span>
            </label>

            <label className="label cursor-pointer gap-2">
              <input 
                type="checkbox" 
                className="toggle" 
                checked={switchChecked}
                onChange={(e) => setSwitchChecked(e.target.checked)}
              />
              <span className="label-text">Switch</span>
            </label>
          </div>

          {/* Slider */}
          <div className="mb-4">
            <label className="label">
              <span className="label-text">Slider Value: {sliderValue}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={sliderValue} 
              className="range range-primary" 
              onChange={(e) => setSliderValue(Number(e.target.value))}
            />
          </div>

          {/* Progress */}
          <div className="mb-4">
            <label className="label">
              <span className="label-text">Progress: {progressValue}%</span>
            </label>
            <progress 
              className="progress progress-primary w-full" 
              value={progressValue} 
              max="100"
            ></progress>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="tabs tabs-boxed mb-6">
        <a 
          className={`tab ${selectedTab === 'tab1' ? 'tab-active' : ''}`}
          onClick={() => setSelectedTab('tab1')}
        >
          Tab 1
        </a>
        <a 
          className={`tab ${selectedTab === 'tab2' ? 'tab-active' : ''}`}
          onClick={() => setSelectedTab('tab2')}
        >
          Tab 2
        </a>
        <a 
          className={`tab ${selectedTab === 'tab3' ? 'tab-active' : ''}`}
          onClick={() => setSelectedTab('tab3')}
        >
          Tab 3
        </a>
      </div>

      {/* Accordion */}
      <div className="join join-vertical w-full mb-6">
        <div className="collapse collapse-arrow join-item border border-base-300">
          <input 
            type="radio" 
            name="my-accordion-4" 
            checked={accordionOpen === 'item1'} 
            onChange={() => toggleAccordion('item1')}
          />
          <div className="collapse-title text-xl font-medium">Accordion Item 1</div>
          <div className="collapse-content">
            <p>Content for accordion item 1</p>
          </div>
        </div>
        <div className="collapse collapse-arrow join-item border border-base-300">
          <input 
            type="radio" 
            name="my-accordion-4" 
            checked={accordionOpen === 'item2'} 
            onChange={() => toggleAccordion('item2')}
          />
          <div className="collapse-title text-xl font-medium">Accordion Item 2</div>
          <div className="collapse-content">
            <p>Content for accordion item 2</p>
          </div>
        </div>
      </div>

      {/* Tooltip, Popover and Dropdown */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="tooltip" data-tip="This is a tooltip">
          <button className="btn">Hover for Tooltip</button>
        </div>
        
        {/* Popover */}
        <div className="popover" ref={popoverRef}>
          <button className="btn" onClick={togglePopover}>Click for Popover</button>
          {popoverOpen && (
            <div className="popover-content bg-base-100 p-4 shadow-lg rounded-box w-64">
              <h3 className="font-bold">Popover Title</h3>
              <p>This is popover content with interactive elements.</p>
              <button className="btn btn-sm btn-primary mt-2" onClick={() => setPopoverOpen(false)}>
                Close
              </button>
            </div>
          )}
        </div>

        {/* Dropdown */}
        <div className="dropdown">
          <div tabIndex={0} role="button" className="btn">Click for Dropdown</div>
          <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
            <li><a>Dropdown Item 1</a></li>
            <li><a>Dropdown Item 2</a></li>
            <li><a>Dropdown Item 3</a></li>
          </ul>
        </div>
      </div>

      {/* Modal */}
      {modalOpen && (
        <dialog className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg">Hello!</h3>
            <p className="py-4">This is a DaisyUI modal component.</p>
            <div className="modal-action">
              <button className="btn" onClick={() => setModalOpen(false)}>Close</button>
            </div>
          </div>
        </dialog>
      )}

      {/* Toast */}
      {toastVisible && (
        <div className="toast toast-top toast-end">
          <div className="alert alert-info">
            <span>This is a toast message!</span>
            <button className="btn btn-sm" onClick={() => setToastVisible(false)}>Dismiss</button>
          </div>
        </div>
      )}
    </div>
  )
}
