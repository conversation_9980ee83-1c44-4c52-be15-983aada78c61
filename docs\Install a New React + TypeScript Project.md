
### **Step 1: Create a New React + TypeScript Project**

To create a new folder named **demo** with React and Typescript preconfigured, run the following command in your terminal:

npm create vite@latest demo -- --template react-ts

You might be prompted to install the necessary Vite packages , just type **“y”** and press **Enter** to proceed.

To navigate into your newly created project directory:

cd demo

### **Step 2: Install Tailwind CSS v4**

Next, install Tailwind CSS and its Vite plugin:

npm install tailwindcss @tailwindcss/vite

After the installation is complete, you can open the project in VS Code:

code .

### **Step 3: Configure Tailwind with Vite**

Open the **vite.config.ts** file and update it to include the Tailwind plugin:

// vite.config.ts  
import { defineConfig } from "vite";  
import react from "@vitejs/plugin-react";  
import tailwindcss from "@tailwindcss/vite"; // Add this line  
  
// https://vite.dev/config/  
export default defineConfig({  
  plugins: [react(), tailwindcss()], // Include the plugin here  
});

This tells Vite to process your styles using Tailwind CSS.


### **Step 4: Import Tailwind CSS**

Create or open your main CSS file (usually **index.css**) and add the Tailwind import:

@import "tailwindcss";

Then, make sure to import that CSS file in your main entry point, typically **main.tsx** or **App.tsx**:

import "./index.css";


### **Step 5: Test Your Setup**

Let’s create a simple React component to confirm everything is working:

import "./index.css";  
  
function App() {  
  return (  
    <div className="min-h-screen flex flex-col justify-center items-center gap-4 bg-sky-700 text-white">  
      <h1 className="text-4xl font-bold">Hello, World!</h1>  
      <p className="text-lg">  
        This is a React + TypeScript + Tailwind CSS v4 project.  
      </p>  
    </div>  
  );  
}  
  
export default App;

This component uses Tailwind utility classes to style it. When you save the file, the styles should automatically apply.


### **Step 6: Run Your App**

Now let’s start the development server:

npm run dev

Your terminal will show a localhost link, click it and open it in your browser. You should see your styled app running live!


### **Final Thoughts**

Well done! You’ve successfully set up a React project with TypeScript and Tailwind CSS v4 using Vite. With fast builds, strong typing, and elegant styling, you’re all set to build with your new frontend stack.

Happy coding! :)