# Front-end Implementation Plan

## Step 1: Set up a new React project with <PERSON>ite and TypeScript

### Create a new React project using Vite with TypeScript template

```bash
npx create-vite@latest frontend --template react-ts
```

### Navigate to the project directory

```bash
cd frontend
```

### Install project dependencies

```bash
npm install
```

## Step 2: Install and configure Tailwind CSS

### Install Tailwind CSS and its dependencies

```bash
npm install -D tailwindcss postcss autoprefixer
```

### Initialize Tailwind CSS configuration

```bash
npx tailwindcss init -p
```

### Configure `tailwind.config.js` to include the correct content paths

```javascript
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

### Update `src/index.css` to include Tailwind directives

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

## Step 3: Install and configure Daisy UI

### Install Daisy UI

```bash
npm i -D daisyui@latest
```

### Add Daisy UI to `tailwind.config.js`

```javascript
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [require("daisyui")],
}
```

### Add Daisy UI to `src/App.css`

```css
@plugin "daisyui";
```

## Step 4: Create a basic layout for the application

### Create a new `App.tsx` file and define the basic layout for the application

### Use Tailwind CSS to style the layout

### Start the development server to test the layout

```bash
npm run dev
```

## Step 5: Implement real-time updates using Server-Sent Events (SSE)

### Set up a new API endpoint to handle SSE requests (backend dependent)

### Use the `EventSource` API to establish a connection to the API endpoint

### Handle incoming events and update the application state accordingly

### Test the real-time functionality with a mock backend or actual Python backend

## Notes

The `npx create-vite@latest frontend --template react-ts` command creates a new React project with Vite and TypeScript without an interactive menu.

### Key Takeaways

1. **Use the correct command**: Use `npx create-vite@latest frontend --template react-ts` to create a new React project with Vite and TypeScript.
2. **Separate Install Step**: Scaffolding only creates files; run `npm install` afterward.
3. **No Direct Package Install**: The command sets up the project structure, not the dependencies.

## Troubleshooting: Tailwind CSS v4 + DaisyUI Integration Issues

### Problem Encountered
When using Tailwind CSS v4 with DaisyUI, the following error occurred:
```
[plugin:vite:css] [postcss] It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.
```

Additionally, DaisyUI components appeared without styling or theming.

### Root Cause
- **Tailwind CSS v4** has a completely different configuration system compared to v3
- The traditional `tailwind.config.js` approach doesn't work with v4
- DaisyUI integration requires the new `@plugin` directive syntax
- PostCSS configuration needed to use `@tailwindcss/postcss` instead of `tailwindcss`

### Solution Applied

#### 1. Updated PostCSS Configuration
The `postcss.config.js` was already correctly configured:
```javascript
export default {
  plugins: {
    '@tailwindcss/postcss': {},
    autoprefixer: {},
  },
}
```

#### 2. Updated CSS Imports for Tailwind CSS v4
Modified `src/index.css` to use the new v4 syntax:
```css
@import "tailwindcss";
@plugin "daisyui";
```

**Note**: The old v3 syntax was:
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

#### 3. Removed Legacy Configuration
- Removed the `tailwind.config.js` file as it's not needed for v4
- Configuration is now done directly in CSS using the `@plugin` directive

#### 4. Added VSCode Settings
Created `.vscode/settings.json` to suppress IDE warnings about unknown `@plugin` rule:
```json
{
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "css.validate": false,
  "postcss.validate": false,
  "scss.lint.unknownAtRules": "ignore"
}
```

### Package Versions Used
- `tailwindcss`: ^4.1.13
- `@tailwindcss/postcss`: ^4.1.13
- `daisyui`: ^5.1.10

### Result
✅ **Fixed**: PostCSS error resolved
✅ **Fixed**: DaisyUI components now display with proper styling and theming
✅ **Fixed**: Development server runs without errors
✅ **Fixed**: IDE warnings suppressed

### Key Differences: Tailwind CSS v3 vs v4

| Aspect | v3 | v4 |
|--------|----|----|
| CSS Imports | `@tailwind base/components/utilities` | `@import "tailwindcss"` |
| Plugin Loading | `plugins: [require('daisyui')]` in config | `@plugin "daisyui"` in CSS |
| Configuration | `tailwind.config.js` file | CSS-based configuration |
| PostCSS Plugin | `tailwindcss` | `@tailwindcss/postcss` |

### References
- [Tailwind CSS v4 + DaisyUI GitHub Discussion](https://github.com/tailwindlabs/tailwindcss/discussions/15828)
- [DaisyUI v5 Beta Documentation](https://v5.daisyui.com/docs/v5-beta/)