


## Create a new project [#](https://flowbite.com/docs/getting-started/typescript/#create-a-new-project)

Follow the next steps to learn how to install TypeScript and Tailwind CSS in a local project. Before continuing make sure that you have Node.js installed locally on your computer.

1. Create a new project and run the following command to create a `package.json` file in the root folder:

```bash
npm init
```

This is where we will add the project dependencies and the script commands.


### Install TypeScript [#](https://flowbite.com/docs/getting-started/typescript/#install-typescript)

By following the official [TypeScript guide](https://www.typescriptlang.org/download) we will install and set it up in our project using NPM.

2. Run the following command to install and require TypeScript as a dependency in your `package.json` file:

```bash
npm install --save-dev typescript
```

3. Create a new `tsconfig.json` file by running the following command:

```bash
npx tsc --init
```

4. Replace the content of the `tsconfig.json` file using the following code:

```javascript
{
  "compilerOptions": {
      "lib": ["dom", "es2015"],
      "outDir": "./lib/cjs/",
      "sourceMap": true,
      "declaration": true,
      "noImplicitAny": true,
      "module": "commonjs",
      "target": "es5",
      "allowJs": true,
      "moduleResolution": "node"
  },
  "include": ["src/**/*.ts*"],
  "exclude": ["node_modules", "dist", "lib"]
}
```


Here’s a breakdown of what each option key-value pair represents:

- `lib` - this option specifies which libraries we want TypeScript to support
- `outDir` - this options tells the TS compiler where to export the compiled code
- `sourceMap` - enables source maps to be generated
- `declaration` - generates declaration files after compiling if true
- `noImplicitAny` - prevents using the any type by throwing an error (this is best practice)
- `module` - specifies the module system to use when generating JS code from the TypeScript source code
- `target` - specifies the JavaScript version to which the TypeScript code should be transpiled
- `allowJs` - specifies whether the compiler should include JS files in the project
- `moduleResolution` - specifies the strategy that the compiler should use to resolve module names
- `include` - specifies which are the source files TypeScript should compile
- `exclude` - specifies which folders should TypeScript ignore when compiling

After setting up the TypeScript configuration file we can now write some code.

5. Set up the folder structure for your TypeScript files by creating a new `src/` folder and creating an `index.ts` file inside of it with the following code:

```javascript
const text: string = 'Hello TypeScript';
console.log(text);
```

This code already uses a type declaration which will help us verify if the compiler works properly.


### Webpack bundler [#](https://flowbite.com/docs/getting-started/typescript/#webpack-bundler)

In order to compile the TypeScript code into JavaScript that is supported by modern browsers we will have to install [Webpack](https://webpack.js.org/) and bundle the source code into one final JavaScript file that we will later include in our HTML templates.

6. Install Webpack and the necessary plugins by executing the following command in your terminal:

```bash
npm i -D webpack webpack-cli typescript ts-loader
```


7. Create a new `webpack.config.js` file and add the following content:

```javascript
//webpack.config.js
const path = require('path');

module.exports = {
  mode: "development",
  devtool: "inline-source-map",
  entry: {
    main: "./src/index.ts",
  },
  output: {
    path: path.resolve(__dirname, './dist'),
    filename: "app-bundle.js" // <--- Will be compiled to this single file
  },
  resolve: {
    extensions: [".ts", ".tsx", ".js"],
  },
  module: {
    rules: [
      { 
        test: /\.tsx?$/,
        loader: "ts-loader"
      }
    ]
  }
};
```

8. Run the following command to watch for changes and compile the TypeScript source code into browser-compatible JavaScript code:

```bash
npx webpack --watch
```

This will generate an `app-bundle.js` named JavaScript file that you can now include inside your HTML templates. To check out if it works you can create a new `index.html` file and open it inside your browser.

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="./dist/output.css">
</head>
<body>
    <h1 class="text-4xl">Hello Tailwind CSS!</h1>
    <script src="./dist/app-bundle.js"></script>
</body>
</html>
```

### CommonJS [#](https://flowbite.com/docs/getting-started/typescript/#commonjs)

By default the compiled code will be CJS as specified in the `tsconfig.json` file.

Compile the code by running the following command in your terminal:

```bash
npx tsc
```

This will generate a new `lib/` folder with CJS (CommonJS) compiled JavaScript code that we will later include in our templates.



Now that we have successfully configured TypeScript and also compiled the source code we have to install and configure Tailwind CSS.

Run the following command to install and require Tailwind CSS in your `package.json` file:

```bash
npm install -D tailwindcss
```

## Install Tailwind CSS [#](https://flowbite.com/docs/getting-started/typescript/#install-tailwind-css)

9. Create a new `tailwind.config.js` file by running the following command:

```bash
npx tailwindcss init
```

10. Based on your source template files make sure you include all of the relevant paths in the `content` area of your Tailwind CSS configuration file:

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.ts",
    "./**/*.html"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

In our case we will look for all TypeScript files inside the `src/` folder and all of the HTML files inside the project relative to the root.


11. Create a new `input.css` file inside the `src/` folder and import all of the basic Tailwind CSS directives:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

12. Compile the code when changes are made by using the following command:

```bash
npx tailwindcss -i ./src/input.css -o ./dist/output.css --watch
```

13. Open the `index.html` file inside the root folder of your project with the following basic setup where we include all of the compiled code including the new `output.css` file:


```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="./dist/output.css">
</head>
<body>
    <h1 class="text-4xl">Hello Tailwind CSS!</h1>
    <script src="./dist/app-bundle.js"></script>
</body>
</html>
```

